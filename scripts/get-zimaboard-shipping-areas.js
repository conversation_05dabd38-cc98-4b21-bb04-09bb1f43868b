#!/usr/bin/env node

const axios = require('axios');
const cheerio = require('cheerio');

async function getShippingCountries() {
  try {
    console.log('Fetching shipping policy page...');

    // Fetch the webpage
    const response = await axios.get('https://www.zimaspace.com/support/shipping-policy', {
      headers: {
        'User-Agent': 'Mozilla/5.0 (Windows NT 10.0; Win64; x64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/91.0.4472.124 Safari/537.36'
      }
    });

    console.log('Page fetched successfully. Parsing HTML...');

    // Load HTML into cheerio
    const $ = cheerio.load(response.data);

    console.log('Looking for country lists in the page...');

    // Based on the actual content, let's extract countries from the known text patterns
    const pageText = $('body').text();

    // Define the country lists as they appear on the page
    const countryLists = [
      'Canada, Mexico, United States, Argentina, Bolivia, Caribbean Netherlands, Chile, Colombia, Ecuador, Peru, Uruguay',
      'Austria, Belgium, Czechia, Denmark, Estonia, Finland, France, Germany, Greece, Hungary, Iceland, Ireland, Italy, Latvia, Luxembourg, Netherlands, Norway, Poland, Portugal, Slovakia, Slovenia, Spain, Sweden, Switzerland, Turkey, United Kingdom, Bulgaria, Croatia, Faroe Islands, Greenland, Guernsey, Liechtenstein, Lithuania, Malta, Moldova, Monaco, Montenegro, Romania, Serbia, Vatican City, Cyprus, Georgia, Isle of Man, San Marino',
      'Australia, Cook Islands, Fiji, New Zealand, Pitcairn Islands, Solomon Islands, Tonga',
      'Bangladesh, China, Hong Kong SAR, India, Indonesia, Israel, Japan, Jordan, Kuwait, Macao SAR, Malaysia, Philippines, Qatar, Saudi Arabia, Singapore, South Korea, Taiwan, Thailand, United Arab Emirates, Vietnam',
      'Cameroon, Egypt, Ethiopia, Kenya, Morocco, Namibia, Nigeria, South Africa, Tunisia, Zambia, Zimbabwe'
    ];

    let allText = '';

    // Check if each country list exists in the page text
    countryLists.forEach((countryList, index) => {
      if (pageText.includes(countryList)) {
        console.log(`Found country list ${index + 1}: ${countryList.substring(0, 50)}...`);
        allText += countryList + ', ';
      }
    });

    // If we didn't find the predefined lists, try to extract dynamically
    if (!allText.trim()) {
      console.log('Predefined lists not found, trying dynamic extraction...');

      // Look for patterns that might contain country lists
      const headings = $('h3, h2').filter((i, el) => {
        const text = $(el).text().toLowerCase();
        return text.includes('america') || text.includes('europe') ||
               text.includes('oceania') || text.includes('asia') || text.includes('africa');
      });

      headings.each((i, heading) => {
        const $heading = $(heading);
        console.log(`Found regional heading: ${$heading.text()}`);

        // Look for the next paragraph or text content
        let nextElement = $heading.next();
        while (nextElement.length > 0) {
          const text = nextElement.text().trim();
          if (text && text.length > 20 && text.includes(',')) {
            console.log(`Found potential country list: ${text.substring(0, 100)}...`);
            allText += text + ', ';
            break;
          }
          nextElement = nextElement.next();
          if (nextElement.is('h1, h2, h3')) break; // Stop at next heading
        }
      });
    }

    // Split by commas and clean up the country names
    const countries = allText
      .split(',')
      .map(country => country.trim())
      .filter(country => country.length > 0)
      .map(country => {
        // Remove any extra whitespace and normalize
        return country.replace(/\s+/g, ' ').trim();
      })
      .filter(country => country.length > 0);

    console.log(`\nExtracted ${countries.length} countries:`);
    countries.forEach((country, index) => {
      console.log(`${index + 1}. ${country}`);
    });

    return countries;

  } catch (error) {
    console.error('Error fetching or parsing the webpage:', error.message);

    if (error.response) {
      console.error('HTTP Status:', error.response.status);
      console.error('HTTP Status Text:', error.response.statusText);
    }

    return [];
  }
}

// Run the function if this script is executed directly
if (require.main === module) {
  getShippingCountries()
    .then(countries => {
      console.log('\n=== FINAL RESULT ===');
      console.log('Countries array:', JSON.stringify(countries, null, 2));
      console.log(`Total countries: ${countries.length}`);
    })
    .catch(error => {
      console.error('Script failed:', error);
      process.exit(1);
    });
}

module.exports = { getShippingCountries };